import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { theme } from "../../config/theme.config";

interface SidebarItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  badge?: number;
  subItems?: SidebarItem[];
}

const sidebarItems: SidebarItem[] = [
  {
    id: "home",
    label: "Home",
    icon: "🏠",
    path: "/",
  },
  {
    id: "summary",
    label: "Summary Dashboard",
    icon: "📊",
    path: "/dashboard/summary",
  },
  {
    id: "contract-awards",
    label: "Contract Awards",
    icon: "📄",
    path: "/dashboard/contract-awards",
  },
  {
    id: "contract-awards-custom",
    label: "Contract Awards (Custom)",
    icon: "🎨",
    path: "/dashboard/contract-awards-custom",
  },
  {
    id: "value-put-in-place",
    label: "Value Put in Place",
    icon: "🏗️",
    path: "/dashboard/value-put-in-place",
  },
  {
    id: "federal-aid",
    label: "Federal-Aid Obligations",
    icon: "🏛️",
    path: "/dashboard/federal-aid",
  },
  {
    id: "state-legislative",
    label: "State Legislative Initiatives",
    icon: "📜",
    path: "/dashboard/state-legislative-initiatives",
  },
  {
    id: "state-dot-budgets",
    label: "State DOT Budgets 2025",
    icon: "💰",
    path: "/dashboard/state-dot-budgets",
  },
  {
    id: "material-prices",
    label: "Material Prices",
    icon: "📈",
    path: "/dashboard/material-prices",
  },
  {
    id: "import",
    label: "Import Dashboard",
    icon: "📥",
    path: "/import",
  },

];

interface SidebarProps {
  isCollapsed?: boolean;
  onToggle?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isCollapsed = false, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemId) ? prev.filter((id) => id !== itemId) : [...prev, itemId]
    );
  };

  const isActive = (path: string) => location.pathname === path;

  const renderSidebarItem = (item: SidebarItem, depth = 0) => {
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = isActive(item.path);

    return (
      <div key={item.id}>
        <div
          onClick={() => {
            if (hasSubItems) {
              toggleExpanded(item.id);
            } else {
              navigate(item.path);
            }
          }}
          style={{
            display: "flex",
            alignItems: "center",
            padding: `${theme.spacing.md}px ${theme.spacing.lg}px`,
            marginLeft: depth * theme.spacing.lg,
            marginBottom: theme.spacing.sm,
            borderRadius: theme.borderRadius.lg,
            cursor: "pointer",
            transition: theme.transitions.normal,
            background: active ? theme.colors.surfaceElevated : "transparent",
            backdropFilter: active ? `blur(${theme.blur.sm})` : "none",
            WebkitBackdropFilter: active ? `blur(${theme.blur.sm})` : "none",
            border: `1px solid ${active ? theme.colors.border.glass : "transparent"}`,
            position: "relative",
            overflow: "hidden",
          }}
          onMouseEnter={(e) => {
            if (!active) {
              e.currentTarget.style.background = theme.colors.surface;
              e.currentTarget.style.backdropFilter = `blur(${theme.blur.sm})`;
              e.currentTarget.style.borderColor = theme.colors.border.light;
            }
          }}
          onMouseLeave={(e) => {
            if (!active) {
              e.currentTarget.style.background = "transparent";
              e.currentTarget.style.backdropFilter = "none";
              e.currentTarget.style.borderColor = "transparent";
            }
          }}
        >
          {active && (
            <div
              style={{
                position: "absolute",
                left: 0,
                top: "50%",
                transform: "translateY(-50%)",
                width: 3,
                height: "70%",
                background: theme.colors.primary,
                borderRadius: theme.borderRadius.sm,
              }}
            />
          )}
          
          <span
            style={{
              fontSize: "20px",
              marginRight: isCollapsed ? 0 : theme.spacing.md,
              filter: active ? "none" : "grayscale(50%)",
              transition: theme.transitions.fast,
            }}
          >
            {item.icon}
          </span>
          
          {!isCollapsed && (
            <>
              <span
                style={{
                  flex: 1,
                  color: active ? theme.colors.text.primary : theme.colors.text.secondary,
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: active ? theme.typography.fontWeight.semibold : theme.typography.fontWeight.normal,
                  transition: theme.transitions.fast,
                }}
              >
                {item.label}
              </span>
              
              {item.badge && (
                <span
                  style={{
                    background: theme.colors.primary,
                    color: theme.colors.text.inverse,
                    fontSize: "11px",
                    fontWeight: theme.typography.fontWeight.semibold,
                    padding: `2px 6px`,
                    borderRadius: theme.borderRadius.full,
                    minWidth: "20px",
                    textAlign: "center",
                  }}
                >
                  {item.badge}
                </span>
              )}
              
              {hasSubItems && (
                <span
                  style={{
                    marginLeft: theme.spacing.sm,
                    transform: isExpanded ? "rotate(90deg)" : "rotate(0)",
                    transition: theme.transitions.fast,
                    color: theme.colors.text.tertiary,
                  }}
                >
                  ▶
                </span>
              )}
            </>
          )}
        </div>
        
        {hasSubItems && isExpanded && !isCollapsed && (
          <div style={{ marginTop: theme.spacing.xs }}>
            {item.subItems!.map((subItem) => renderSidebarItem(subItem, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      style={{
        width: isCollapsed ? "80px" : "280px",
        height: "100vh",
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.xl})`,
        WebkitBackdropFilter: `blur(${theme.blur.xl})`,
        borderRight: `1px solid ${theme.colors.border.glass}`,
        display: "flex",
        flexDirection: "column",
        transition: theme.transitions.normal,
        position: "fixed",
        left: 0,
        top: 0,
        zIndex: 100,
      }}
    >
      {/* Logo Section */}
      <div
        style={{
          padding: theme.spacing.lg,
          borderBottom: `1px solid ${theme.colors.border.glass}`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: theme.spacing.md }}>
          <div
            style={{
              width: 40,
              height: 40,
              background: theme.colors.gradients.primary,
              borderRadius: theme.borderRadius.lg,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "20px",
              color: "white",
              fontWeight: theme.typography.fontWeight.bold,
              boxShadow: theme.shadows.md,
            }}
          >
            A
          </div>
          {!isCollapsed && (
            <span
              style={{
                fontSize: theme.typography.fontSize.lg,
                fontWeight: theme.typography.fontWeight.bold,
                color: theme.colors.text.primary,
              }}
            >
              ARTBA
            </span>
          )}
        </div>
        
        <button
          onClick={onToggle}
          style={{
            background: "transparent",
            border: "none",
            cursor: "pointer",
            padding: theme.spacing.sm,
            borderRadius: theme.borderRadius.md,
            color: theme.colors.text.secondary,
            transition: theme.transitions.fast,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = theme.colors.surfaceElevated;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "transparent";
          }}
        >
          {isCollapsed ? "→" : "←"}
        </button>
      </div>

      {/* Navigation Items */}
      <div
        style={{
          flex: 1,
          padding: theme.spacing.lg,
          overflowY: "auto",
        }}
      >
        {sidebarItems.map((item) => renderSidebarItem(item))}
      </div>

      {/* User Section */}
      <div
        style={{
          padding: theme.spacing.lg,
          borderTop: `1px solid ${theme.colors.border.glass}`,
          background: theme.colors.surfaceElevated,
          backdropFilter: `blur(${theme.blur.md})`,
          WebkitBackdropFilter: `blur(${theme.blur.md})`,
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: theme.spacing.md,
            padding: `${theme.spacing.sm}px 0`,
            cursor: "pointer",
            borderRadius: theme.borderRadius.md,
            transition: theme.transitions.fast,
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = theme.colors.surfaceHover;
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "transparent";
          }}
        >
          <div
            style={{
              width: 36,
              height: 36,
              borderRadius: "50%",
              background: theme.colors.gradients.primary,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              color: "white",
              fontSize: theme.typography.fontSize.sm,
              fontWeight: theme.typography.fontWeight.semibold,
              flexShrink: 0,
            }}
          >
            JD
          </div>
          {!isCollapsed && (
            <div style={{ flex: 1 }}>
              <div
                style={{
                  fontSize: theme.typography.fontSize.sm,
                  fontWeight: theme.typography.fontWeight.semibold,
                  color: theme.colors.text.primary,
                }}
              >
                John Doe
              </div>
              <div
                style={{
                  fontSize: theme.typography.fontSize.xs,
                  color: theme.colors.text.secondary,
                }}
              >
                Administrator
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};