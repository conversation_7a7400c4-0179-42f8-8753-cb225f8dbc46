import React, { Component, ErrorInfo, ReactNode } from "react";
import { WidgetById } from "@sisense/sdk-ui";
import { WidgetContainer } from "../../layout/WidgetContainer";
import type { WidgetPosition } from "../../../types/dashboard.types";
import { theme } from "../../../config/theme.config";

interface SisenseWidgetProps {
  widgetId: string;
  dashboardId: string;
  title?: string;
  position: WidgetPosition;
  includeDashboardFilters?: boolean;
  widgetType?: 'chart' | 'kpi' | 'table' | 'pivot';
  styleOptions?: {
    height?: number;
    width?: number;
    [key: string]: unknown;
  };
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class WidgetErrorBoundary extends Component<
  { children: ReactNode },
  ErrorBoundaryState
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Widget Error:", error, errorInfo);
    // Check if it's the CRS error
    if (error.message?.includes("CRS")) {
      console.error("Map-related CRS error detected in widget");
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          style={{
            padding: theme.spacing.xl,
            textAlign: "center",
            background: theme.colors.surface,
            borderRadius: theme.borderRadius.lg,
            border: `1px solid ${theme.colors.border.light}`,
          }}
          data-oid="jbj054b"
        >
          <div
            style={{
              fontSize: "24px",
              marginBottom: theme.spacing.md,
              opacity: 0.5,
            }}
            data-oid="5cdwxn8"
          >
            ⚠️
          </div>
          <div
            style={{
              fontSize: theme.typography.fontSize.sm,
              color: theme.colors.text.secondary,
            }}
            data-oid="zfj:t.v"
          >
            Widget temporarily unavailable
          </div>
          {this.state.error?.message?.includes("CRS") && (
            <div
              style={{
                fontSize: theme.typography.fontSize.xs,
                color: theme.colors.text.tertiary,
                marginTop: theme.spacing.sm,
              }}
              data-oid="hgmojyu"
            >
              Map component initialization error
            </div>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export const SisenseWidget: React.FC<SisenseWidgetProps> = ({
  widgetId,
  dashboardId,
  title,
  position,
  includeDashboardFilters = true,
  widgetType = 'chart',
  styleOptions,
}) => {
  // Calculate dynamic height based on widget position
  const defaultHeight = position.h * 120; // Approximate height per grid row

  const widgetStyleOptions = {
    height: defaultHeight,
    width: styleOptions?.width || undefined,
    ...styleOptions,
  };

  return (
    <WidgetContainer title={title} position={position} widgetType={widgetType} data-oid="bkb4_7z">
      <WidgetErrorBoundary data-oid="c8:hac1">
        <WidgetById
          widgetOid={widgetId}
          dashboardOid={dashboardId}
          includeDashboardFilters={includeDashboardFilters}
          styleOptions={widgetStyleOptions}
          data-oid="namlriz"
        />
      </WidgetErrorBoundary>
    </WidgetContainer>
  );
};
