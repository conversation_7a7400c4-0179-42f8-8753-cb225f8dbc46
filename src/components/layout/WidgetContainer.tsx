import React from "react";
import { theme } from "../../config/theme.config";
import type { WidgetPosition } from "../../types/dashboard.types";

interface WidgetContainerProps {
  children: React.ReactNode;
  title?: string;
  position: WidgetPosition;
  loading?: boolean;
  error?: Error | null;
  widgetType?: 'chart' | 'kpi' | 'table' | 'pivot';
}

export const WidgetContainer: React.FC<WidgetContainerProps> = ({
  children,
  title,
  position,
  loading = false,
  error = null,
  widgetType = 'chart',
}) => {
  const gridStyles = {
    gridColumn: `span ${position.w}`,
    gridRow: `span ${position.h}`,
    '--widget-width': position.w.toString(),
  } as React.CSSProperties;

  return (
    <div
      className={`widget-${widgetType}`}
      style={{
        ...gridStyles,
        background: theme.colors.surface,
        backdropFilter: `blur(${theme.blur.lg})`,
        WebkitBackdropFilter: `blur(${theme.blur.lg})`,
        borderRadius: theme.borderRadius.lg,
        boxShadow: theme.shadows.glass,
        padding: theme.spacing.lg,
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
        transition: `all ${theme.transitions.normal}`,
        border: `1px solid ${theme.colors.border.glass}`,
        minHeight: `${position.h * 120}px`,
        height: "auto",
        position: "relative",
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.cardHover;
        e.currentTarget.style.transform = "translateY(-4px) scale(1.02)";
        e.currentTarget.style.background = theme.colors.surfaceElevated;
        e.currentTarget.style.borderColor = theme.colors.border.DEFAULT;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.boxShadow = theme.shadows.glass;
        e.currentTarget.style.transform = "translateY(0) scale(1)";
        e.currentTarget.style.background = theme.colors.surface;
        e.currentTarget.style.borderColor = theme.colors.border.glass;
      }}
      data-oid="yer6zvp"
    >
      {title && (
        <h3
          style={{
            margin: `0 0 ${theme.spacing.md}px 0`,
            color: theme.colors.text.primary,
            fontSize: theme.typography.fontSize.md,
            fontWeight: theme.typography.fontWeight.medium,
            letterSpacing: "-0.2px",
          }}
          data-oid="ttgfn_:"
        >
          {title}
        </h3>
      )}
      <div
        style={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: loading || error ? "center" : "stretch",
          minHeight: 0,
          width: "100%",
        }}
        data-oid="j5t7c7b"
      >
        {loading && (
          <div
            style={{
              color: theme.colors.text.secondary,
              fontSize: theme.typography.fontSize.sm,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: theme.spacing.md,
            }}
            data-oid="dm11w3j"
          >
            <div
              style={{
                width: "40px",
                height: "40px",
                border: `2px solid ${theme.colors.border.glass}`,
                borderTopColor: theme.colors.primary,
                borderRadius: "50%",
                animation: "spin 1s linear infinite",
              }}
              data-oid="ng.-g:x"
            />
            Loading widget...
          </div>
        )}
        {error && (
          <div
            style={{
              color: theme.colors.error,
              textAlign: "center",
              fontSize: theme.typography.fontSize.sm,
              padding: theme.spacing.md,
              background: "rgba(239, 68, 68, 0.1)",
              borderRadius: theme.borderRadius.md,
              backdropFilter: `blur(${theme.blur.sm})`,
              WebkitBackdropFilter: `blur(${theme.blur.sm})`,
              border: `1px solid rgba(239, 68, 68, 0.2)`,
            }}
            data-oid="n1rq6:x"
          >
            Error: {error.message}
          </div>
        )}
        {!loading && !error && children}
      </div>
      <style data-oid="uxgfdhv">
        {`
          @keyframes spin {
            to { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};
