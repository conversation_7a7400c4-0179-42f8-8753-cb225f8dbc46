export const theme = {
  colors: {
    primary: '#ff6b35',
    primaryLight: '#ff8c5a',
    primaryDark: '#e55a2b',
    secondary: '#ffa947',
    secondaryLight: '#ffbc6e',
    secondaryDark: '#e89936',
    accent: '#ff4757',
    accentLight: '#ff6b7a',
    background: {
      primary: '#fff5eb',
      secondary: '#ffebd4',
      tertiary: '#ffe0bd',
      blur: 'rgba(255, 245, 235, 0.85)',
    },
    backgroundGradient: 'linear-gradient(135deg, #ffebcd 0%, #ffdab9 25%, #ffcba4 50%, #ffc19e 75%, #ffb894 100%)',
    surface: 'rgba(255, 255, 255, 0.6)',
    surfaceElevated: 'rgba(255, 255, 255, 0.7)',
    surfaceHover: 'rgba(255, 255, 255, 0.8)',
    glass: {
      primary: 'rgba(255, 255, 255, 0.6)',
      secondary: 'rgba(255, 255, 255, 0.7)',
      tertiary: 'rgba(255, 255, 255, 0.8)',
      border: 'rgba(255, 255, 255, 0.5)',
      dark: 'rgba(255, 235, 220, 0.4)',
    },
    error: '#ff4757',
    warning: '#ffa502',
    info: '#3742fa',
    success: '#2ed573',
    text: {
      primary: '#2c2c2c',
      secondary: '#5a5a5a',
      tertiary: '#7a7a7a',
      disabled: '#9a9a9a',
      inverse: '#ffffff',
    },
    border: {
      light: 'rgba(255, 255, 255, 0.4)',
      DEFAULT: 'rgba(255, 255, 255, 0.6)',
      dark: 'rgba(255, 255, 255, 0.8)',
      glass: 'rgba(255, 255, 255, 0.5)',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #ff6b35 0%, #ff8c5a 100%)',
      secondary: 'linear-gradient(135deg, #ffa947 0%, #ffbc6e 100%)',
      accent: 'linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%)',
      warm: 'linear-gradient(135deg, #ff6348 0%, #ff8c5a 100%)',
      cool: 'linear-gradient(135deg, #4bcffa 0%, #0abde3 100%)',
      success: 'linear-gradient(135deg, #2ed573 0%, #26d0ce 100%)',
      surface: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%)',
      glass: 'linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%)',
      glow: 'linear-gradient(135deg, rgba(255, 107, 53, 0.2) 0%, rgba(255, 169, 71, 0.2) 100%)',
    },
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
    widget: 20,
    dashboard: 28,
    section: 40,
  },
  borderRadius: {
    xs: 2,
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
    xxl: 24,
    widget: 12,
    card: 16,
    full: '9999px',
  },
  shadows: {
    xs: '0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04)',
    sm: '0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.04)',
    md: '0 8px 16px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.04)',
    lg: '0 15px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.04)',
    xl: '0 20px 40px rgba(0, 0, 0, 0.15)',
    widget: '0 8px 24px rgba(0, 0, 0, 0.08)',
    card: '0 8px 24px rgba(0, 0, 0, 0.08)',
    cardHover: '0 12px 32px rgba(0, 0, 0, 0.12)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.04)',
    glass: '0 8px 32px 0 rgba(255, 107, 53, 0.15)',
    glow: '0 0 20px rgba(255, 107, 53, 0.3)',
    none: 'none',
  },
  blur: {
    none: '0',
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '20px',
    xxl: '40px',
  },
  transitions: {
    fast: '150ms ease-in-out',
    normal: '300ms ease-in-out',
    slow: '500ms ease-in-out',
    bounce: '300ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  typography: {
    fontFamily: {
      sans: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      mono: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
    },
    fontSize: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
      xxl: '24px',
      xxxl: '32px',
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeight: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.75,
    },
  },
  breakpoints: {
    xs: 0,
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    xxl: '1536px',
  },
} as const;