import { useExecuteQueryByWidgetId,useGetWidgetModel } from "@sisense/sdk-ui";
import { PageLayout } from "../../components/layout/PageLayout";
export const Test: React.FC = () => {
  const { widget, isLoading, isError  } =  useGetWidgetModel({
    widgetOid: '686d7f81099a11833ea61b4e',
    dashboardOid: '686d7f81099a11833ea61b3b'
  });
  if (isLoading) {
    return <div>Loading...</div>;
  }
  if (isError) {
    return <div>Error</div>;
  }
  if (widget) {
    console.log(widget.dataOptions);
  }

  return (
    <PageLayout title="Test Page">
      <div>
        <h1>Test Page</h1>
      </div>
    </PageLayout>
  );
};