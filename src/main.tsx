import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { SisenseContextProvider } from "@sisense/sdk-ui";

// Global error handler for debugging CRS error
window.addEventListener("error", (event) => {
  if (event.error?.message?.includes("CRS")) {
    console.error("CRS Error Detected:", {
      message: event.error.message,
      stack: event.error.stack,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
    // Prevent the default error handling to avoid breaking the app
    event.preventDefault();
  }
});

// Also catch unhandled promise rejections
window.addEventListener("unhandledrejection", (event) => {
  if (event.reason?.message?.includes("CRS")) {
    console.error("CRS Promise Rejection:", event.reason);
    event.preventDefault();
  }
});

createRoot(document.getElementById("root")!).render(
  <StrictMode data-oid="kg8ah50">
    <SisenseContextProvider
      url={import.meta.env.VITE_INSTANCE_URL}
      ssoEnabled={false}
      token={import.meta.env.VITE_TOKEN}
      data-oid="mqct7wt"
    >
      <App data-oid="lmnsb58" />
    </SisenseContextProvider>
  </StrictMode>,
);
